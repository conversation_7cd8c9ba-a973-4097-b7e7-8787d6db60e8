"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.findActiveSeekers = exports.updateFlnScore = exports.findSeekersByLocation = exports.findSeekersBySkills = exports.getAllSeekers = exports.deleteSeeker = exports.updateSeekerByAppwriteId = exports.updateSeeker = exports.findByAppwriteId = exports.findById = exports.createSeeker = void 0;
const mongodb_1 = require("mongodb");
const db_1 = require("../config/db");
const seekerCollection = () => db_1.db.collection("seekers");
const createSeeker = async (seekerData) => {
    // Check if seeker with this appwriteId already exists
    const existingSeeker = await (0, exports.findByAppwriteId)(seekerData.appwriteId);
    if (existingSeeker) {
        throw new Error("Seeker with this Appwrite ID already exists");
    }
    const newSeeker = {
        ...seekerData,
        phoneVerified: false,
        education: seekerData.education || [],
        workExperience: seekerData.workExperience || [],
        skills: seekerData.skills || [],
        languages: seekerData.languages || [],
        certifications: seekerData.certifications || [],
        portfolioLinks: seekerData.portfolioLinks || [],
        availability: seekerData.availability || {},
        preferences: seekerData.preferences || {},
        isActive: true,
        isDeleted: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    };
    const result = await seekerCollection().insertOne(newSeeker);
    return { ...newSeeker, _id: result.insertedId.toString() };
};
exports.createSeeker = createSeeker;
const findById = async (seekerId) => {
    if (!mongodb_1.ObjectId.isValid(seekerId)) {
        return null;
    }
    return await seekerCollection().findOne({
        _id: new mongodb_1.ObjectId(seekerId),
        isDeleted: false,
    });
};
exports.findById = findById;
const findByAppwriteId = async (appwriteId) => {
    return await seekerCollection().findOne({
        appwriteId,
        isDeleted: false,
    });
};
exports.findByAppwriteId = findByAppwriteId;
const updateSeeker = async (seekerId, updateData) => {
    if (!mongodb_1.ObjectId.isValid(seekerId)) {
        return null;
    }
    const updatedSeeker = {
        $set: {
            ...updateData,
            updatedAt: new Date(),
        },
    };
    await seekerCollection().updateOne({ _id: new mongodb_1.ObjectId(seekerId), isDeleted: false }, updatedSeeker);
    return await (0, exports.findById)(seekerId);
};
exports.updateSeeker = updateSeeker;
const updateSeekerByAppwriteId = async (appwriteId, updateData) => {
    const updatedSeeker = {
        $set: {
            ...updateData,
            updatedAt: new Date(),
        },
    };
    await seekerCollection().updateOne({ appwriteId, isDeleted: false }, updatedSeeker);
    return await (0, exports.findByAppwriteId)(appwriteId);
};
exports.updateSeekerByAppwriteId = updateSeekerByAppwriteId;
const deleteSeeker = async (seekerId) => {
    if (!mongodb_1.ObjectId.isValid(seekerId)) {
        return false;
    }
    const result = await seekerCollection().updateOne({ _id: new mongodb_1.ObjectId(seekerId), isDeleted: false }, { $set: { isDeleted: true, updatedAt: new Date() } });
    return result.modifiedCount > 0;
};
exports.deleteSeeker = deleteSeeker;
const getAllSeekers = async (page = 1, limit = 10, filters = {}) => {
    const skip = (page - 1) * limit;
    const query = { isDeleted: false, ...filters };
    const seekers = await seekerCollection()
        .find(query)
        .skip(skip)
        .limit(limit)
        .toArray();
    const total = await seekerCollection().countDocuments(query);
    return { seekers, total };
};
exports.getAllSeekers = getAllSeekers;
const findSeekersBySkills = async (skills) => {
    return await seekerCollection()
        .find({
        isDeleted: false,
        isActive: true,
        "skills.name": { $in: skills },
    })
        .toArray();
};
exports.findSeekersBySkills = findSeekersBySkills;
const findSeekersByLocation = async (location) => {
    return await seekerCollection()
        .find({
        isDeleted: false,
        isActive: true,
        $or: [
            { "preferences.locations": { $in: [location] } },
            { "preferences.remoteWork": true },
        ],
    })
        .toArray();
};
exports.findSeekersByLocation = findSeekersByLocation;
const updateFlnScore = async (seekerId, flnScore) => {
    if (!mongodb_1.ObjectId.isValid(seekerId)) {
        return null;
    }
    await seekerCollection().updateOne({ _id: new mongodb_1.ObjectId(seekerId) }, {
        $set: {
            flnScore,
            updatedAt: new Date(),
        },
    });
    return await (0, exports.findById)(seekerId);
};
exports.updateFlnScore = updateFlnScore;
const findActiveSeekers = async () => {
    return await seekerCollection()
        .find({
        isDeleted: false,
        isActive: true,
    })
        .toArray();
};
exports.findActiveSeekers = findActiveSeekers;
