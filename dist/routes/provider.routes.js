"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const provider_controller_1 = require("../controllers/provider.controller");
const router = express_1.default.Router();
// Provider gig management routes
// Note: All routes are already protected by authenticateUser and providerAuthMiddleware in index.ts
// Get all gigs for the provider
router.get("/gigs", provider_controller_1.getProviderGigs);
// Create a new gig (List Your Gig)
router.post("/gigs", provider_controller_1.createGig);
// Get a specific gig
router.get("/gigs/:gigId", provider_controller_1.getGig);
// Update a specific gig
router.patch("/gigs/:gigId", provider_controller_1.updateGig);
// Delete a specific gig
router.delete("/gigs/:gigId", provider_controller_1.deleteGig);
exports.default = router;
