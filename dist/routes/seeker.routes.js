"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const seeker_controller_1 = require("../controllers/seeker.controller");
const router = express_1.default.Router();
// Seeker onboarding and profile management routes
// Note: All routes are already protected by authenticateUser and seekerAuthMiddleware in index.ts
// Get seeker profile
router.get("/profile", seeker_controller_1.getSeekerProfile);
// Create initial seeker profile
router.post("/profile", seeker_controller_1.createSeekerProfile);
// Update complete seeker profile
router.patch("/profile", seeker_controller_1.updateSeekerProfile);
// Onboarding step-by-step routes
// Step 1: Basic information (name, date of birth, gender, phone)
router.patch("/onboarding/basic-info", seeker_controller_1.updateBasicInfo);
// Step 2: Location preferences and current location
router.patch("/onboarding/location", seeker_controller_1.updateLocationPreferences);
// Step 3: Education details
router.patch("/onboarding/education", seeker_controller_1.updateEducationDetails);
// Step 4: Skills
router.patch("/onboarding/skills", seeker_controller_1.updateSkills);
// Step 5: Resume upload
router.patch("/onboarding/resume", seeker_controller_1.uploadResume);
exports.default = router;
