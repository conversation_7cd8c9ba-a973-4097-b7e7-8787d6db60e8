# 🚀 Giggle Backend API Testing with Postman - Complete Setup

## ✅ Server Status
- **Server**: ✅ Running on `http://localhost:5000`
- **Database**: ✅ Connected to MongoDB
- **Status**: Ready for API testing

## 📁 Files Created for Testing

### 1. Postman Collection
**File**: `Giggle_Backend_API_Tests.postman_collection.json`
- Complete collection with all 8 API endpoints
- Pre-configured request bodies and headers
- Environment variable support

### 2. Postman Environment  
**File**: `Giggle_Backend_Environment.postman_environment.json`
- Pre-configured environment variables
- Includes sample JWT token from your test.http file
- Easy variable management

### 3. Testing Guide
**File**: `API_Testing_Guide.md`
- Comprehensive testing instructions
- Endpoint documentation
- Troubleshooting guide

### 4. Server Test Script
**File**: `test_server.js`
- Quick server connectivity test
- Useful for debugging

## 🎯 Quick Start Guide

### Step 1: Import to Postman
1. Open Postman
2. Click **Import** button
3. Import both files:
   - `Giggle_Backend_API_Tests.postman_collection.json`
   - `Giggle_Backend_Environment.postman_environment.json`

### Step 2: Set Environment
1. Select "Giggle Backend Environment" from environment dropdown
2. Update the `jwt_token` variable with a valid Appwrite JWT token

### Step 3: Start Testing
Test endpoints in this order:
1. **Health Check**: GET `/api/seeker/` or `/api/provider/`
2. **Register User**: POST `/api/users/register`
3. **Get Profile**: GET `/api/users/profile`
4. **Update Profile**: PATCH `/api/users/profile`

## 📋 All Available Endpoints

| Method | Endpoint | Auth Required | Description |
|--------|----------|---------------|-------------|
| POST | `/api/users/register` | ✅ JWT | Register new user |
| GET | `/api/users/profile` | ✅ JWT | Get user profile |
| PATCH | `/api/users/profile` | ✅ JWT | Update user profile |
| DELETE | `/api/users/profile` | ✅ JWT | Delete user profile |
| POST | `/api/users/send-verification-email` | ✅ JWT | Send verification email |
| GET | `/api/users/verify-user` | ❌ | Verify user email |
| GET | `/api/seeker/` | ✅ JWT + Seeker | Seeker example endpoint |
| GET | `/api/provider/` | ✅ JWT + Provider | Provider example endpoint |

## 🔑 Authentication Notes

### JWT Token Setup
- Get JWT token from Appwrite authentication
- Format: `Bearer {your_jwt_token}`
- Update `jwt_token` environment variable in Postman

### User Types
- **SEEKER**: Access to `/api/seeker/*` endpoints
- **PROVIDER**: Access to `/api/provider/*` endpoints
- **Note**: Current auth middleware defaults to "SEEKER" type

## 🧪 Sample Test Data

### Register User Request Body:
```json
{
  "name": "John Doe",
  "profile": {
    "bio": "Software Developer",
    "location": "New York"
  }
}
```

### Update Profile Request Body:
```json
{
  "name": "John Smith",
  "profile": {
    "bio": "Senior Software Developer",
    "location": "San Francisco",
    "skills": ["JavaScript", "TypeScript", "Node.js"]
  }
}
```

## 🔧 Environment Variables

| Variable | Value | Description |
|----------|-------|-------------|
| `base_url` | `http://localhost:5000` | Server base URL |
| `jwt_token` | Your JWT token | Authentication token |
| `user_id` | User ID | For verification endpoints |
| `verification_secret` | Secret code | Email verification secret |

## ⚠️ Important Notes

1. **Server Must Be Running**: Ensure `npm run dev` is running
2. **Valid JWT Required**: Most endpoints require valid Appwrite JWT
3. **User Type Restrictions**: Seeker/Provider endpoints have role restrictions
4. **Database Connection**: MongoDB must be accessible

## 🐛 Troubleshooting

### Common HTTP Status Codes:
- **200**: ✅ Success
- **401**: ❌ Invalid/missing JWT token
- **403**: ❌ Wrong user type (Seeker vs Provider)
- **404**: ❌ User not found
- **409**: ❌ User already exists
- **500**: ❌ Server error

### Quick Fixes:
1. **401 Errors**: Update JWT token in environment
2. **403 Errors**: Check user type in auth middleware
3. **Connection Errors**: Verify server is running on port 5000

## 🎉 Ready to Test!

Your Giggle Backend API is now fully set up for comprehensive testing with Postman. The server is running, all endpoints are documented, and you have everything needed to test the complete API functionality.

**Next Steps:**
1. Import the Postman files
2. Update JWT token
3. Start testing endpoints
4. Use the testing guide for detailed instructions

Happy testing! 🚀
