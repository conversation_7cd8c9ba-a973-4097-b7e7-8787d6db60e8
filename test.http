@baseUrl = http://localhost:6969

# Variables for environment configuration
@port = 6969
@host = localhost
@apiPrefix = api
@your_jwt_token_here = eyJpZCI6IjY4MzY0NmJlMDAyZGFhZWQwOGIzIiwic2VjcmV0IjoiNjdiY2EwOGFiOGRlNmVmMjU0NWNkNDI0MTVlNDUwZDliMDk3M2VhNGE2OGRlMDE1NWY3MmZkN2RjM2JmM2UzMWYyYmVjMzhiMGI1MzVkMjU5YmMxMTZkNWIyNzczZDNmNDJiODMwZjFkMDU4MjFiMDk5ZDMwMWVkYTUxMjBmNzQxZDU3ODZjMThmM2I1NzVkZDc0NWNhZTY4NzI0MDg2OWM2Njc4ODExNDc3OGFhZTYyZTcxODU2YjQzZmM2MTllZjA4N2JiOWIzZjdjMWVhZmIzODhhZjBmY2NhNDVkNWNkNDcyZjg3ZWFiZmVkZmQxYjU1NjBjZDg4OTk4MjA2ZSJ9

###

# 🛡️ Test protected route with JWT
GET {{baseUrl}}/{{apiPrefix}}/example
Authorization: Bearer {{your_jwt_token_here}}
Content-Type: application/json

###

# 👤 Register a new user in the backend
POST {{baseUrl}}/{{apiPrefix}}/users/register
Authorization: Bearer {{your_jwt_token_here}}
Content-Type: application/json

{
  "name": "John Doe",
  "profile": {
    "bio": "Software developer and tech enthusiast",
    "phoneNumber": "+1234567890",
    "avatar": "https://example.com/avatar.jpg"
  }
}

###

# 👤 Get user profile
GET {{baseUrl}}/{{apiPrefix}}/users/profile
Authorization: Bearer {{your_jwt_token_here}}
Content-Type: application/json

###

# 👤 Update user profile
PATCH {{baseUrl}}/{{apiPrefix}}/users/profile
Authorization: Bearer {{your_jwt_token_here}}
Content-Type: application/json

{
  "name": "<PERSON> Updated",
  "profile": {
    "bio": "Senior software developer and tech enthusiast",
    "phoneNumber": "+1234567890"
  }
}

###

# 👤 Delete user (soft delete)
DELETE {{baseUrl}}/{{apiPrefix}}/users/profile
Authorization: Bearer {{your_jwt_token_here}}
Content-Type: application/json

###

# 🎯 SEEKER ONBOARDING API TESTS

# 👤 Get seeker profile
GET {{baseUrl}}/{{apiPrefix}}/seeker/profile
Authorization: Bearer {{your_jwt_token_here}}
Content-Type: application/json

###

# 👤 Create seeker profile (initial onboarding)
POST {{baseUrl}}/{{apiPrefix}}/seeker/profile
Authorization: Bearer {{your_jwt_token_here}}
Content-Type: application/json

{
  "name": "John Seeker",
  "phoneNumber": "+91 **********",
  "educationLevel": "bachelor",
  "employmentStatus": "unemployed"
}

###

# 📝 Step 1: Update basic information
PATCH {{baseUrl}}/{{apiPrefix}}/seeker/onboarding/basic-info
Authorization: Bearer {{your_jwt_token_here}}
Content-Type: application/json

{
  "name": "John Seeker",
  "dateOfBirth": "2000-06-15",
  "gender": "male",
  "phoneNumber": "+91 **********"
}

###

# 📍 Step 2: Update location preferences
PATCH {{baseUrl}}/{{apiPrefix}}/seeker/onboarding/location
Authorization: Bearer {{your_jwt_token_here}}
Content-Type: application/json

{
  "preferences": {
    "locations": ["Mumbai", "Delhi", "Bangalore"],
    "remoteWork": true,
    "willingToRelocate": true
  }
}

###

# 🎓 Step 3: Update education details
PATCH {{baseUrl}}/{{apiPrefix}}/seeker/onboarding/education
Authorization: Bearer {{your_jwt_token_here}}
Content-Type: application/json

{
  "educationLevel": "bachelor",
  "degree": "Bachelor of Technology",
  "specialization": "Computer Science",
  "completionYear": "2025",
  "university": "Indian Institute of Technology"
}

###

# 🛠️ Step 4: Update skills
PATCH {{baseUrl}}/{{apiPrefix}}/seeker/onboarding/skills
Authorization: Bearer {{your_jwt_token_here}}
Content-Type: application/json

{
  "skills": [
    {
      "name": "customer service",
      "level": "intermediate",
      "yearsOfExperience": 2
    },
    {
      "name": "Photography",
      "level": "advanced",
      "yearsOfExperience": 3
    },
    {
      "name": "Hospitality",
      "level": "beginner",
      "yearsOfExperience": 1
    },
    {
      "name": "Communication",
      "level": "advanced",
      "yearsOfExperience": 4
    },
    {
      "name": "Marketing",
      "level": "intermediate",
      "yearsOfExperience": 2
    }
  ]
}

###

# 📄 Step 5: Upload resume
PATCH {{baseUrl}}/{{apiPrefix}}/seeker/onboarding/resume
Authorization: Bearer {{your_jwt_token_here}}
Content-Type: application/json

{
  "resumeLink": "https://example.com/resume.pdf",
  "resumeFileId": "file_123456789"
}

###

# 👤 Update complete seeker profile (all fields)
PATCH {{baseUrl}}/{{apiPrefix}}/seeker/profile
Authorization: Bearer {{your_jwt_token_here}}
Content-Type: application/json

{
  "name": "John Seeker Updated",
  "phoneNumber": "+91 **********",
  "dateOfBirth": "2000-06-15",
  "gender": "male",
  "educationLevel": "bachelor",
  "employmentStatus": "unemployed",
  "skills": [
    {
      "name": "customer service",
      "level": "intermediate",
      "yearsOfExperience": 2
    }
  ],
  "preferences": {
    "locations": ["Mumbai", "Delhi"],
    "remoteWork": true,
    "willingToRelocate": false
  }
}

###

# 🏢 PROVIDER GIG MANAGEMENT API TESTS

# 📋 Get all gigs for provider
GET {{baseUrl}}/{{apiPrefix}}/provider/gigs
Authorization: Bearer {{your_jwt_token_here}}
Content-Type: application/json

###

# ➕ Create a new gig (List Your Gig)
POST {{baseUrl}}/{{apiPrefix}}/provider/gigs
Authorization: Bearer {{your_jwt_token_here}}
Content-Type: application/json

{
  "companyName": "Tech Solutions Inc",
  "jobRole": "Quality Assurance",
  "salary": "50000",
  "jobType": "full-time",
  "requiredGiggleGrade": "G+",
  "otherRequirements": "Experience with testing frameworks and attention to detail",
  "facilities": "Health insurance, Flexible hours, Remote work",
  "remoteWork": false
}

###

# 📄 Get a specific gig
GET {{baseUrl}}/{{apiPrefix}}/provider/gigs/REPLACE_WITH_GIG_ID
Authorization: Bearer {{your_jwt_token_here}}
Content-Type: application/json

###

# ✏️ Update a specific gig
PATCH {{baseUrl}}/{{apiPrefix}}/provider/gigs/REPLACE_WITH_GIG_ID
Authorization: Bearer {{your_jwt_token_here}}
Content-Type: application/json

{
  "companyName": "Tech Solutions Inc Updated",
  "jobRole": "Senior Quality Assurance",
  "salary": "60000",
  "jobType": "full-time",
  "requiredGiggleGrade": "G+",
  "otherRequirements": "5+ years experience with testing frameworks and attention to detail",
  "facilities": "Health insurance, Flexible hours, Remote work, Gym membership",
  "remoteWork": true
}

###

# 🗑️ Delete a specific gig
DELETE {{baseUrl}}/{{apiPrefix}}/provider/gigs/REPLACE_WITH_GIG_ID
Authorization: Bearer {{your_jwt_token_here}}
Content-Type: application/json

###

# 🚧 Example placeholder for another endpoint
# GET {{host}}:{{port}}/{{apiPrefix}}/users
# Authorization: Bearer {{your_jwt_token_here}}
# Content-Type: application/json
