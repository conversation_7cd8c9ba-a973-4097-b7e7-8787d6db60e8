const http = require('http');

// Test server connectivity
function testServerConnection() {
    const options = {
        hostname: 'localhost',
        port: 5000,
        path: '/api/users/profile',
        method: 'GET',
        headers: {
            'Authorization': 'Bearer test_token',
            'Content-Type': 'application/json'
        }
    };

    const req = http.request(options, (res) => {
        console.log(`Server is running on port 5000`);
        console.log(`Status Code: ${res.statusCode}`);
        console.log(`Headers:`, res.headers);
        
        let data = '';
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            console.log('Response:', data);
        });
    });

    req.on('error', (error) => {
        console.log(`❌ Server connection failed:`, error.message);
        console.log('Make sure the server is running with: npm run dev');
    });

    req.end();
}

console.log('🔍 Testing Giggle Backend Server Connection...');
testServerConnection();
