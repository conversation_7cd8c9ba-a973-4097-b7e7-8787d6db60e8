# Giggle Backend API Testing Guide

## Overview
This guide provides comprehensive instructions for testing all API endpoints in the Giggle Backend using Postman.

## Server Configuration
- **Base URL**: `http://localhost:5000`
- **Default Port**: 5000 (configurable via .env)
- **Authentication**: JWT Bearer tokens via Appwrite

## Prerequisites

### 1. Start the Server
```bash
# Development mode
npm run dev

# Production mode
npm run build
npm start
```

### 2. Import Postman Collection
1. Open Postman
2. Click "Import" 
3. Select the `Giggle_Backend_API_Tests.postman_collection.json` file
4. The collection will be imported with all endpoints

### 3. Set Environment Variables
In Postman, create a new environment or update the collection variables:

- `base_url`: `http://localhost:5000`
- `jwt_token`: Your valid JWT token from Appwrite
- `user_id`: User ID for verification endpoints
- `verification_secret`: Secret for email verification

## API Endpoints Overview

### User Management Endpoints (`/api/users`)

#### 1. Register User
- **Method**: POST
- **URL**: `/api/users/register`
- **Auth**: Required (JWT <PERSON> token)
- **Body**:
```json
{
  "name": "<PERSON> Doe",
  "profile": {
    "bio": "Software Developer",
    "location": "New York"
  }
}
```

#### 2. Get User Profile
- **Method**: GET
- **URL**: `/api/users/profile`
- **Auth**: Required (JWT Bearer token)

#### 3. Update User Profile
- **Method**: PATCH
- **URL**: `/api/users/profile`
- **Auth**: Required (JWT Bearer token)
- **Body**:
```json
{
  "name": "John Smith",
  "profile": {
    "bio": "Senior Software Developer",
    "location": "San Francisco",
    "skills": ["JavaScript", "TypeScript", "Node.js"]
  }
}
```

#### 4. Delete User Profile
- **Method**: DELETE
- **URL**: `/api/users/profile`
- **Auth**: Required (JWT Bearer token)

#### 5. Send Verification Email
- **Method**: POST
- **URL**: `/api/users/send-verification-email`
- **Auth**: Required (JWT Bearer token)
- **Body**: `{}`

#### 6. Verify User
- **Method**: GET
- **URL**: `/api/users/verify-user?userId={userId}&secret={secret}`
- **Auth**: Not required
- **Query Parameters**:
  - `userId`: User ID from verification email
  - `secret`: Secret from verification email

### Seeker Endpoints (`/api/seeker`)

#### 7. Seeker Example Endpoint
- **Method**: GET
- **URL**: `/api/seeker/`
- **Auth**: Required (JWT Bearer token + Seeker role)
- **Note**: User type must be "SEEKER"

### Provider Endpoints (`/api/provider`)

#### 8. Provider Example Endpoint
- **Method**: GET
- **URL**: `/api/provider/`
- **Auth**: Required (JWT Bearer token + Provider role)
- **Note**: User type must be "PROVIDER"

## Authentication Setup

### Getting JWT Token
1. Use Appwrite SDK to authenticate and get JWT token
2. The token should be in format: `Bearer {your_jwt_token}`
3. Set this in the `jwt_token` environment variable

### User Types
- **SEEKER**: Can access `/api/seeker/*` endpoints
- **PROVIDER**: Can access `/api/provider/*` endpoints
- **Note**: Currently, auth middleware hardcodes user type as "SEEKER"

## Testing Workflow

### 1. Basic Health Check
Start with the example endpoints to verify server connectivity:
- Test `/api/seeker/` (requires SEEKER token)
- Test `/api/provider/` (requires PROVIDER token)

### 2. User Registration Flow
1. **Register User**: POST `/api/users/register`
2. **Get Profile**: GET `/api/users/profile`
3. **Update Profile**: PATCH `/api/users/profile`
4. **Send Verification**: POST `/api/users/send-verification-email`
5. **Verify User**: GET `/api/users/verify-user` (use params from email)

### 3. Profile Management
1. Create user with minimal data
2. Update with complete profile information
3. Verify profile retrieval
4. Test profile deletion (use with caution)

## Common Response Formats

### Success Response
```json
{
  "success": true,
  "message": "Operation successful",
  "data": { ... }
}
```

### Error Response
```json
{
  "error": "Error message",
  "success": false
}
```

## Troubleshooting

### Common Issues

1. **401 Unauthorized**
   - Check JWT token validity
   - Ensure Bearer prefix in Authorization header
   - Verify Appwrite configuration

2. **403 Forbidden**
   - Check user type (SEEKER vs PROVIDER)
   - Verify middleware configuration

3. **404 Not Found**
   - Verify endpoint URL
   - Check if user exists in database

4. **500 Internal Server Error**
   - Check server logs
   - Verify database connection
   - Check Appwrite configuration

### Debug Tips
- Enable console logging in auth middleware
- Check server terminal for detailed error messages
- Verify environment variables in `.env` file
- Test with valid Appwrite JWT tokens

## Environment Variables Required
```
PORT=5000
MONGO_URI=your_mongodb_connection_string
APPWRITE_ENDPOINT=your_appwrite_endpoint
APPWRITE_PROJECT_ID=your_project_id
APPWRITE_API_KEY=your_api_key
```

## Next Steps
After basic testing, consider:
1. Adding more comprehensive gig management endpoints
2. Implementing seeker and provider specific functionality
3. Adding file upload endpoints
4. Creating application management endpoints
