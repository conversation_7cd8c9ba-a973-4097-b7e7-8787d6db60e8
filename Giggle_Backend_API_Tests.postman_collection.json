{"info": {"_postman_id": "giggle-backend-api-tests", "name": "Giggle Backend API Tests", "description": "Complete API testing collection for Giggle Backend with all endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "User Management", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"profile\": {\n    \"bio\": \"Software Developer\",\n    \"location\": \"New York\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/users/register", "host": ["{{base_url}}"], "path": ["api", "users", "register"]}}}, {"name": "Get User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/users/profile", "host": ["{{base_url}}"], "path": ["api", "users", "profile"]}}}, {"name": "Update User Profile", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"profile\": {\n    \"bio\": \"Senior Software Developer\",\n    \"location\": \"San Francisco\",\n    \"skills\": [\"JavaScript\", \"TypeScript\", \"Node.js\"]\n  }\n}"}, "url": {"raw": "{{base_url}}/api/users/profile", "host": ["{{base_url}}"], "path": ["api", "users", "profile"]}}}, {"name": "Delete User Profile", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/users/profile", "host": ["{{base_url}}"], "path": ["api", "users", "profile"]}}}, {"name": "Send Verification Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{base_url}}/api/users/send-verification-email", "host": ["{{base_url}}"], "path": ["api", "users", "send-verification-email"]}}}, {"name": "Verify User", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/users/verify-user?userId={{user_id}}&secret={{verification_secret}}", "host": ["{{base_url}}"], "path": ["api", "users", "verify-user"], "query": [{"key": "userId", "value": "{{user_id}}"}, {"key": "secret", "value": "{{verification_secret}}"}]}}}]}, {"name": "Seeker Endpoints", "item": [{"name": "Seeker Example Endpoint", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/seeker/", "host": ["{{base_url}}"], "path": ["api", "seeker", ""]}}}]}, {"name": "Provider Endpoints", "item": [{"name": "Provider Example Endpoint", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/provider/", "host": ["{{base_url}}"], "path": ["api", "provider", ""]}}}]}], "variable": [{"key": "base_url", "value": "http://localhost:5000", "type": "string"}, {"key": "jwt_token", "value": "your_jwt_token_here", "type": "string"}, {"key": "user_id", "value": "your_user_id_here", "type": "string"}, {"key": "verification_secret", "value": "your_verification_secret_here", "type": "string"}]}